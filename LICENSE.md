Apache License
Version 2.0, January 2004
http://www.apache.org/licenses/

Copyright 2025 biex

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

ADDITIONAL TERMS:

In addition to the terms of the Apache License 2.0, the following
attribution requirements apply to any use, modification, or distribution
of this software:

1. MANDATORY AUTHOR ATTRIBUTION:
   - The original author "biex" MUST be prominently credited in any
     derivative work, modification, or distribution
   - This credit must appear in the software's user interface, documentation,
     and any promotional materials
   - The credit format must be: "Based on LittleWhiteBox by biex"

2. PROJECT ATTRIBUTION:
   - The project name "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" and "小白X" must be credited
   - Project URL must be included: https://github.com/RT15548/LittleWhiteBox

3. TRADEMARK AND BRANDING:
   - The name "Little<PERSON><PERSON><PERSON><PERSON><PERSON>" and "小白X" are trademarks of the original author
   - Derivative works may not use these names without explicit permission

4. ATTRIBUTION IN DERIVATIVE WORKS:
   - Any modification or enhancement must clearly indicate it is based on
     the original work by "biex"
   - Source attribution must be maintained in all copies

VIOLATION OF THESE TERMS:
Any violation of these attribution requirements will result in immediate
termination of the license grant.

License: Apache-2.0 WITH Custom-Attribution-Requirements

For the complete Apache License 2.0 text, see:
http://www.apache.org/licenses/LICENSE-2.0
